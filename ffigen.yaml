# Run with `dart run ffigen --config ffigen.yaml`.
name: StockfishChessEngineBindings
description: |
  Bindings for `src/stockfish_chess_engine.h`.

  Regenerate bindings with `dart run ffigen --config ffigen.yaml`.
output: "lib/stockfish_chess_engine_bindings_generated.dart"
headers:
  entry-points:
    - "src/stockfish_chess_engine.h"
  include-directives:
    - "src/stockfish_chess_engine.h"
preamble: |
  // ignore_for_file: always_specify_types
  // ignore_for_file: camel_case_types
  // ignore_for_file: non_constant_identifier_names
comments:
  style: any
  length: full
llvm-path:
  - "/usr/lib/llvm-19"
